<?php

declare(strict_types=1);

namespace App\Console\Commands\Task;

use App\Enums\Settings\Task\TaskTypesEnum;
use App\Models\Task\Task;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

/**
 * <AUTHOR>
 */
class CompleteTasksOfConfirmedInspectionRepresentativesCommand extends Command
{
    /**
     * @var string
     */
    protected $signature = 'adept:complete-tasks-of-confirmed-inspection-representatives';

    /**
     * @var string
     */
    protected $description = 'Complete tasks of confirmed inspection representatives';

    /**
     * @return void
     */
    public function handle(): void
    {
        $taskIdsToComplete = Task::query()
            ->with('parentInspection.participants')
            ->where('type', TaskTypesEnum::INSPECTIONS_CONFIRM_PARTICIPATION->value)
            ->whereNull('completion_date')
            ->whereHas(
                'parentInspection.participants',
                static function (Builder $query): void {
                    $query
                        ->where('is_confirmed', true)
                        ->whereColumn('representative_id', 'tasks.representative_id');
                },
            )
            ->pluck('id');

        $this->info('Найдено ' . count($taskIdsToComplete) . ' задач для завершения');

        
    }
}
