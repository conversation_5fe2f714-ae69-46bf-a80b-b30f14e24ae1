<?php

declare(strict_types=1);

namespace App\Services;

use App\DTO\Filters\InspectionRepresentativeFilterDTO;
use App\DTO\Inspection\Representative\CancelParticipationDTO;
use App\DTO\Inspection\Representative\DeviationDoc\AttachRepresentativeDeviationDocDTO;
use App\DTO\Inspection\Representative\DeviationDoc\DetachRepresentativeDeviationDocDTO;
use App\DTO\InspectionImageDTO;
use App\DTO\UpdateData\InspectionRepresentativeDTO;
use App\DTO\User\RepresentativeDataDTO;
use App\Enums\Settings\Task\TaskTypesEnum;
use App\Exceptions\ChangePropertyDeniedException;
use App\Exceptions\CreateEntityDeniedException;
use App\Exceptions\DeleteEntityDeniedException;
use App\Exceptions\Exception as AdeptException;
use App\Exceptions\Inspections\InspectionException;
use App\Exceptions\Inspections\Representative\InspectionCancellationException;
use App\Exceptions\Inspections\Representative\NotAbleToCancelParticipationException;
use App\Exceptions\Inspections\Representative\ParticipationIsCanceledException;
use App\Exceptions\Inspections\Representative\RepresentativeDeviationDocsAttachingExpection;
use App\Exceptions\Settings\General\GeneralSettingException;
use App\Exceptions\Settings\Inspection\SettingInspectionParticipantIsBusyException;
use App\Exceptions\Signatures\DeleteSignatureDeniedException;
use App\Exceptions\Users\PowerOfAttorney\PowerOfAttorneyIsOutOfDateException;
use App\Exceptions\Users\PowerOfAttorney\PowerOfAttorneyIsUnderReviewException;
use App\Exceptions\Users\PowerOfAttorney\PowerOfAttorneyNeedException;
use App\Exceptions\WrongBehavior;
use App\Extensions\Authorize\AuthUserTrait;
use App\Extensions\Inspection\InspectionRepresentativeServiceTrait;
use App\Extensions\Notificationable\NotificationableModelTrait;
use App\Extensions\Permissions\DocumentPartnersPermissionTrait;
use App\Extensions\Permissions\PermissionTrait;
use App\Jobs\Document\UpdateOrCreateStampedDocumentVersionJob;
use App\Models\Document\Document;
use App\Models\Inspection\Inspection;
use App\Models\Inspection\InspectionRepresentative;
use App\Models\Inspection\InspectionRepresentativeImage;
use App\Models\Inspection\InspectionRepresentativeRole;
use App\Models\Inspection\InspectionSituation;
use App\Models\Inspection\InspectionStatus;
use App\Models\Partner\PartnerBindPermission;
use App\Models\Settings\Inspection\SettingInspectionChangeStatusLazyBlock;
use App\Models\User\Permission;
use App\Models\User\Representative;
use App\Modules\EventLog\EventLogService;
use App\Modules\EventLog\EventLogSettings;
use App\Repositories\InspectionRepresentativeRole\InspectionRepresentativeRoleRepository;
use App\Repositories\Inspections\InspectionRepository;
use App\Repositories\Inspections\InspectionRepresentativeRepository;
use App\Repositories\InspectionStatus\InspectionStatusRepository;
use App\Repositories\Representative\RepresentativeRepository;
use App\Services\Settings\Inspection\SettingInspectionParticipantIntervalService;
use App\Services\Task\TaskService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Throwable;

/**
 * Class InspectionRepresentativeService
 */
class InspectionRepresentativeService
{
    use AuthUserTrait;
    use DocumentPartnersPermissionTrait;
    use InspectionRepresentativeServiceTrait;
    use NotificationableModelTrait;
    use PermissionTrait;

    /** @var string */
    private const string ALWAYS_BLOCKED_MESSAGE = 'Невозможно изменить статус!';

    /** @var TaskService */
    protected TaskService $taskService;

    /** @var mixed|SignatureService */
    private SignatureService $signatureService;

    /** @var FileService */
    private FileService $fileService;

    /** @var InspectionStatusRepository */
    private InspectionStatusRepository $inspectionStatusRepository;

    /** @var InspectionRepresentativeRoleRepository */
    private InspectionRepresentativeRoleRepository $inspectionRepresentativeRoleRepository;

    /** @var UserService */
    private UserService $userService;

    /** @var EventLogService */
    private EventLogService $eventLogService;

    /**
     * @param TaskService $taskService
     * @param FileService $fileService
     * @param InspectionStatusRepository $inspectionStatusRepository
     * @param InspectionRepresentativeRoleRepository $inspectionRepresentativeRoleRepository
     * @param InspectionRepository $inspectionRepository
     * @param InspectionRepresentativeRepository $inspectionRepresentativeRepository
     * @param SettingInspectionParticipantIntervalService $participantIntervalService
     * @param RepresentativeRepository $representativeRepository
     * @param UserService $userService
     * @param EventLogService $eventLogService
     *
     * @throws BindingResolutionException
     */
    public function __construct(
        TaskService $taskService,
        FileService $fileService,
        InspectionStatusRepository $inspectionStatusRepository,
        InspectionRepresentativeRoleRepository $inspectionRepresentativeRoleRepository,
        private readonly InspectionRepository $inspectionRepository,
        private readonly InspectionRepresentativeRepository $inspectionRepresentativeRepository,
        public readonly SettingInspectionParticipantIntervalService $participantIntervalService,
        private readonly RepresentativeRepository $representativeRepository,
        UserService $userService,
        EventLogService $eventLogService,
    ) {
        $this->signatureService = app()->make(SignatureService::class);
        $this->taskService = $taskService;
        $this->fileService = $fileService;
        $this->inspectionRepresentativeRoleRepository = $inspectionRepresentativeRoleRepository;
        $this->inspectionStatusRepository = $inspectionStatusRepository;
        $this->userService = $userService;
        $this->eventLogService = $eventLogService;
    }

    /**
     * @param int $inspectionId
     * @param InspectionRepresentativeFilterDTO $filter
     *
     * @return Collection
     */
    public function getInspectionRepresentatives(
        int $inspectionId,
        InspectionRepresentativeFilterDTO $filter,
    ): Collection {
        $inspectionRepresentatives = InspectionRepresentative::with(
            [
                'representative.partner',
                'images',
                'inspectionRemarks.remark.inspectionType',
                'inspectionRemarks.remark.pnrRemarkCategory',
                'inspectionRemarks.remark.remarkCategory',
                'inspectionRemarks.images',
                'statusAfterElimination',
                'deviationDocs',
                'cancelReason.parent',
            ],
        )->where('inspection_id', $inspectionId);
        $inspectionRepresentatives->filtrate($filter);

        return $inspectionRepresentatives->orderBy('id')->get();
    }

    /**
     * @param int $id
     *
     * @return InspectionRepresentative
     */
    public function getInspectionRepresentative(int $id): InspectionRepresentative
    {
        /** @var InspectionRepresentative */
        return InspectionRepresentative::with(
            [
                'representative.partner',
                'statusAfterElimination',
                'images' => function ($query) {
                    return $query->orderBy('created_at', 'desc')->get();
                },
                'cancelReason.parent',
            ],
        )->findOrFail($id);
    }

    /**
     * @param InspectionRepresentativeDTO $inspectionRepresentativeDTO
     * @param int $inspectionRepresentativeId
     * @param int $objectId
     * @param bool $existSignerLetter
     *
     * @return InspectionRepresentative
     *
     * @throws AdeptException
     * @throws DeleteSignatureDeniedException
     * @throws Exception
     * @throws GeneralSettingException
     * @throws InspectionCancellationException
     * @throws ParticipationIsCanceledException
     * @throws PowerOfAttorneyIsOutOfDateException
     * @throws PowerOfAttorneyIsUnderReviewException
     * @throws PowerOfAttorneyNeedException
     * @throws WrongBehavior
     */
    public function updateSmrInspectionRepresentative(
        InspectionRepresentativeDTO $inspectionRepresentativeDTO,
        int $inspectionRepresentativeId,
        int $objectId,
        bool $existSignerLetter,
    ): InspectionRepresentative {
        if (! $inspectionRepresentativeDTO->hasProperty('signer_id')) {
            $this->checkPermission(Permission::DOMAIN_INSPECTIONS, Permission::PERMISSION_EDIT);
        }

        /** @var InspectionRepresentative $inspectionRepresentative */
        $inspectionRepresentative = InspectionRepresentative::query()
            ->with(['inspection.author.representative.partner'])
            ->findOrFail($inspectionRepresentativeId);

        if (isset($inspectionRepresentative->cancel_date)) {
            throw new ParticipationIsCanceledException();
        }

        $this->checkDocumentPartnersPermissionForSign(
            $inspectionRepresentative->inspection,
            $inspectionRepresentativeDTO,
            ['is_confirmed'],
        );

        $oldStatus = $this->preparePreviousInspectionStatus($inspectionRepresentative);

        $inspectionRepresentative = $this
            ->getRepresentativeInfo($inspectionRepresentative, $inspectionRepresentativeDTO);

        $this->updateStatus($inspectionRepresentative, $inspectionRepresentativeDTO);

        $this->updateStatusAfterElimination($inspectionRepresentative, $inspectionRepresentativeDTO);

        $this->updateCancelReason($inspectionRepresentative, $inspectionRepresentativeDTO);

        $inspectionRepresentative = $this->updateInspectionRepresentative(
            $inspectionRepresentativeDTO,
            $inspectionRepresentative,
            TaskTypesEnum::INSPECTIONS_CONFIRM_PARTICIPATION->value,
            true,
            $objectId,
        );

        $inspectionRepresentativeDTO = $inspectionRepresentativeDTO->except(
            'status_id',
            'signer_id',
            'role_id',
            'cancel_reason_id',
        );

        /** @var InspectionRepresentative $inspectionRepresentative */
        $inspectionRepresentative->propagateFromDTO($inspectionRepresentativeDTO);

        $inspectionRepresentative = $this->checkInspectionRepresentative($inspectionRepresentative);

        $inspectionRepresentative->save();

        $newStatus = $this->prepareCurrentInspectionStatus($inspectionRepresentative);

        $action = null;

        if ($oldStatus !== $newStatus) {
            $action = EventLogSettings::RESULT_INSPECTION_SMR_EVENT;
        }

        if ($inspectionRepresentativeDTO->signer_id !== null) {
            $action = $existSignerLetter === true
            || $inspectionRepresentativeDTO->is_confirmed === true
                ? EventLogSettings::CONFIRM_PARTICIPATION_IN_INSPECTION_SMR_EVENT
                : EventLogSettings::AGREE_INSPECTION_SMR_EVENT;
        }

        if ($action !== null) {
            $this->prepareAndSendDataEventLogInspection(
                $inspectionRepresentative->inspection,
                $action,
                $oldStatus !== $newStatus ? $oldStatus : null,
                $oldStatus !== $newStatus ? $newStatus : null,
            );
        }

        return $inspectionRepresentative->loadMissing(
            [
                'statusAfterElimination',
                'status',
                'deviationDocs.currentVersion.documentsStatus',
                'cancelReason.parent',
            ],
        );
    }

    /**
     * @param Inspection $inspection
     * @param Representative $representative
     *
     * @return InspectionRepresentative
     */
    public function createInspectionRepresentative(Inspection $inspection, Representative $representative): InspectionRepresentative
    {
        $inspectionRepresentative = new InspectionRepresentative();

        $inspectionRepresentative->inspection()->associate($inspection);
        $inspectionRepresentative->representative()->associate($representative);
        $inspectionRepresentative->inspectionRepresentativeRole()->associate(
            $this->getInspectionRepresentativeRoleFromSettings($representative),
        );

        $inspectionRepresentative->save();

        return $inspectionRepresentative;
    }

    /**
     * @param int $inspectionId
     * @param array $representativeIds
     *
     * @return Collection
     *
     * @throws CreateEntityDeniedException
     * @throws InspectionException
     * @throws Throwable
     */
    public function createInspectionRepresentatives(int $inspectionId, array $representativeIds): Collection
    {
        /** @var Inspection $inspection */
        $inspection = Inspection::query()
            ->with(
                [
                    'author.representative.partner',
                    'author.representative.user',
                ],
            )
            ->findOrFail($inspectionId);

        $inspection->checkBlockedInspection();
        $inspection->checkDocumentPartnersPermission(PartnerBindPermission::PERMISSION_EDIT);

        $inspectionRepresentativeAlreadyExists = InspectionRepresentative::query()
            ->where('inspection_id', $inspectionId)
            ->whereIn('representative_id', $representativeIds)
            ->exists();

        if ($inspectionRepresentativeAlreadyExists) {
            throw new CreateEntityDeniedException('Представитель уже прикреплен');
        }

        $representatives = Representative::query()
            ->whereIn('id', $representativeIds)
            ->get();

        if (null !== $inspection->planned_date_begin) {
            $this->checkIfRepresentativesCanBeAddedToInspection(
                $inspection,
                $representatives,
            );
        }

        $inspectionRepresentatives = new Collection();

        /** @var Representative $representative */
        foreach ($representatives as $representative) {
            $inspectionRepresentative = $this->createInspectionRepresentative($inspection, $representative);
            $inspectionRepresentatives->add($inspectionRepresentative);
        }

        $this->calculateInspectionRepresentativesStatuses(
            $inspectionRepresentatives,
            $inspection,
        );

        return $inspectionRepresentatives;
    }

    /**
     * @param int $inspectionId
     * @param int $inspectionRepresentativeId
     *
     * @return void
     *
     * @throws DeleteEntityDeniedException
     * @throws Exception
     */
    public function deleteInspectionRepresentative(int $inspectionId, int $inspectionRepresentativeId): void
    {
        /** @var Inspection $inspection */
        $inspection = Inspection::query()
            ->with(['author.representative.partner'])
            ->findOrFail($inspectionId);
        $inspection->checkDocumentPartnersPermission(PartnerBindPermission::PERMISSION_EDIT);
        if ($inspection->hasSign()) {
            throw new DeleteEntityDeniedException('Удаление участников отклонено');
        }
        InspectionRepresentative::findOrFail($inspectionRepresentativeId)->delete();
    }

    /**
     * @param array|int[] $ids
     * @param int $inspectionId
     *
     * @throws Exception
     */
    public function deleteInspectionRepresentatives(array $ids, int $inspectionId): void
    {
        $representatives = InspectionRepresentative::query()
            ->with(['inspection.author.representative.partner'])
            ->find($ids);

        $permissionToDeleteFull = $this->hasPermission(
            Permission::DOMAIN_INSPECTIONS,
            Permission::PERMISSION_FULL,
        );

        /** @var InspectionRepresentative $representative */
        foreach ($representatives as $representative) {
            if (false === $permissionToDeleteFull) {
                $representative->inspection->checkBlockedInspection();
            }
            if (null !== $representative->status_id) {
                throw InspectionException::changeRejected();
            }
            $representative->inspection->checkDocumentPartnersPermission(PartnerBindPermission::PERMISSION_EDIT);
            $this->taskService->closeTask(
                $inspectionId,
                $representative->representative_id,
                TaskTypesEnum::INSPECTIONS_CONFIRM_PARTICIPATION->value,
                true,
            );
        }
        InspectionRepresentative::whereIn('id', $ids)
            ->get()
            ->filter(fn(InspectionRepresentative $inspectionRepresentative) => ! $inspectionRepresentative->hasSign())
            ->map(fn(InspectionRepresentative $inspectionRepresentative) => $inspectionRepresentative->delete());

        $inspection = $this->inspectionRepository->findOrFail($inspectionId);
        $this->getCalculateInspectionStatusService()->updateInspectionStatus($inspection);
    }

    /**
     * @param InspectionImageDTO $image
     * @param int $inspectionRepresentativeId
     *
     * @return InspectionRepresentativeImage
     */
    public function attachImage(
        InspectionImageDTO $image,
        int $inspectionRepresentativeId,
    ): InspectionRepresentativeImage {
        $representative = InspectionRepresentative::findOrFail($inspectionRepresentativeId);
        $inspectionImage = new InspectionRepresentativeImage();
        $inspectionImage->path = $image->file->getPath();
        $inspectionImage->representative()->associate($representative);
        $inspectionImage->save();

        return $inspectionImage;
    }

    /**
     * @param int $imageId
     *
     * @throws Exception
     */
    public function detachImage(int $imageId): void
    {
        $image = InspectionRepresentativeImage::with('representative')->findOrFail($imageId);
        $count = InspectionRepresentativeImage::where('path', $image->path)->count();
        if ($count === 1) {
            Storage::delete($image->path);
        }
        $image->delete();
    }

    /**
     * @param int $imageId
     *
     * @return InspectionRepresentativeImage
     */
    public function getImage(int $imageId): InspectionRepresentativeImage
    {
        return InspectionRepresentativeImage::findOrFail($imageId);
    }

    /**
     * @param int $representativeId
     *
     * @return Collection
     */
    public function getImages(int $representativeId): Collection
    {
        return InspectionRepresentativeImage::with(['representative'])
            ->where('representative_id', $representativeId)
            ->get();
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     * @param InspectionRepresentativeDTO $inspectionRepresentativeDTO
     *
     * @return InspectionRepresentative
     */
    public function getRepresentativeInfo(
        InspectionRepresentative $inspectionRepresentative,
        InspectionRepresentativeDTO $inspectionRepresentativeDTO,
    ): InspectionRepresentative {
        if (
            ($inspectionRepresentative->status_id === null
                && $inspectionRepresentativeDTO->status_id)
            || ($inspectionRepresentative->status_after_elimination_id === null
                && $inspectionRepresentativeDTO->status_after_elimination_id)
        ) {
            $representative = $inspectionRepresentative->representative;
            if ($representative) {
                $representativeData = RepresentativeDataDTO::createFromRepresentative($representative);
                $inspectionRepresentative->representative_info = $representativeData->toJson();
            }
        }

        return $inspectionRepresentative;
    }

    /**
     * @param int $inspectionRepresentativeId
     * @param CancelParticipationDTO $dto
     *
     * @return InspectionRepresentative
     *
     * @throws Exception
     * @throws NotAbleToCancelParticipationException
     */
    public function cancelParticipation(
        int $inspectionRepresentativeId,
        CancelParticipationDTO $dto,
    ): InspectionRepresentative {
        $inspectionRepresentative = InspectionRepresentative::findOrFail($inspectionRepresentativeId);
        if ($inspectionRepresentative->is_confirmed === true) {
            throw new ChangePropertyDeniedException();
        }
        if (! $this->isInspectionRepresentativeAbleToCancelParticipation($inspectionRepresentative)) {
            throw new NotAbleToCancelParticipationException();
        }

        $this->cancelInspectionRepresentativeParticipation($inspectionRepresentative, $dto->comment);
        $this->taskService->closeTask(
            $inspectionRepresentative->inspection->getKey(),
            $inspectionRepresentative->representative_id,
            TaskTypesEnum::INSPECTIONS_CONFIRM_PARTICIPATION->value,
        );

        return $inspectionRepresentative;
    }

    /**
     * @param int $inspectionRepresentativeId
     *
     * @return void
     *
     * @throws ParticipationIsCanceledException
     */
    public function checkAbleToUpdate(int $inspectionRepresentativeId): void
    {
        $inspectionRepresentative = InspectionRepresentative::findOrFail($inspectionRepresentativeId);
        if (isset($inspectionRepresentative->cancel_date)) {
            throw new ParticipationIsCanceledException();
        }
    }

    /**
     * @param int $inspectionRepresentativeId
     * @param AttachRepresentativeDeviationDocDTO $dto
     *
     * @return Collection
     *
     * @throws RepresentativeDeviationDocsAttachingExpection
     */
    public function attachHasDeviationDocs(
        int $inspectionRepresentativeId,
        AttachRepresentativeDeviationDocDTO $dto,
    ): Collection {
        $inspectionRepresentative = InspectionRepresentative::findOrFail($inspectionRepresentativeId);
        $documents = Document::query()->withoutGlobalScope('public')->findOrFail($dto->document_ids);
        $this->checkDeviationDocsAbleToAttach($inspectionRepresentative->inspection, $documents);
        $attachInfo = $inspectionRepresentative->deviationDocs()->syncWithoutDetaching($documents);

        return Document::query()->whereIn('id', $attachInfo['attached'])->get();
    }

    /**
     * @param int $inspectionRepresentativeId
     * @param DetachRepresentativeDeviationDocDTO $dto
     *
     * @return Collection
     */
    public function detachDeviationDocs(
        int $inspectionRepresentativeId,
        DetachRepresentativeDeviationDocDTO $dto,
    ): Collection {
        $inspectionRepresentative = InspectionRepresentative::findOrFail($inspectionRepresentativeId);
        $documents = Document::query()->withoutGlobalScope('public')->findOrFail($dto->document_ids);
        $inspectionRepresentative->deviationDocs()->detach($documents);
        $inspectionRepresentative->load('deviationDocs');

        return $inspectionRepresentative->deviationDocs;
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     *
     * @return bool
     */
    public function checkDeviationStampAvailability(InspectionRepresentative $inspectionRepresentative): bool
    {
        return $inspectionRepresentative->status
            && $inspectionRepresentative->status->name === InspectionStatus::STATUS_HAS_DEVIATIONS
            && $inspectionRepresentative->inspection->status
            && $inspectionRepresentative->inspection->status->name === InspectionStatus::STATUS_HAS_DEVIATIONS;
    }

    /**
     * @param Collection $documents
     *
     * @return void
     */
    public function updateOrCreateStampsOnDocumentsWithDeviations(Collection $documents): void
    {
        $documents->each(
            fn(Document $document) => UpdateOrCreateStampedDocumentVersionJob::dispatch($document->currentVersion),
        );
    }

    /**
     * @param Inspection $inspection
     * @param Collection $representatives
     *
     * @return void
     *
     * @throws Exception
     */
    private function checkIfRepresentativesCanBeAddedToInspection(
        Inspection $inspection,
        Collection $representatives,
    ): void {
        $participantInterval = $this
            ->participantIntervalService
            ->getOrCreateSettingInspectionParticipantInterval(
                $inspection->object->company_id,
            );

        if (false === $participantInterval->is_enabled) {
            return;
        }

        $interval = $participantInterval->carbon_interval;

        if ($interval->hour === 0 && $interval->minute === 0) {
            $higherInterval = $inspection
                ->planned_date_end
                ->addDay()
                ->format("Y-m-d\TH:i:s");
            $lowerInterval = $inspection
                ->planned_date_begin
                ->subDay()
                ->format("Y-m-d\TH:i:s");
        } else {
            $higherInterval = $inspection
                ->planned_date_end
                ->addHours($interval->hour)
                ->addMinutes($interval->minute)
                ->format("Y-m-d\TH:i:s");
            $lowerInterval = $inspection
                ->planned_date_begin
                ->subHours($interval->hour)
                ->subMinutes($interval->minute)
                ->format("Y-m-d\TH:i:s");
        }

        if (
            null === $higherInterval
            || null === $lowerInterval
        ) {
            return;
        }

        $existsBusyRepresentative = $this
            ->inspectionRepository
            ->existsForDateTimeIntervalByParticipantIdsAndExceptId(
                $representatives->pluck('id'),
                $lowerInterval,
                $higherInterval,
                $inspection->getKey(),
            );

        if (false === $existsBusyRepresentative) {
            return;
        }

        throw new SettingInspectionParticipantIsBusyException();
    }

    /**
     * @param Collection $inspectionRepresentatives
     * @param Inspection $inspection
     *
     * @return void
     *
     * @throws Throwable
     */
    private function calculateInspectionRepresentativesStatuses(
        Collection $inspectionRepresentatives,
        Inspection $inspection,
    ): void {
        /** @var InspectionRepresentative $firstInspectionRepresentative */
        $firstInspectionRepresentative = $inspectionRepresentatives->first();
        if ($firstInspectionRepresentative === null) {
            return;
        }
        $companyId = $firstInspectionRepresentative->inspection->object->company_id;
        $acceptStatus = $this->inspectionStatusRepository->getStatusAccept($companyId);
        $roleSkContractor = $this
            ->inspectionRepresentativeRoleRepository
            ->findInspectionRepresentativeRoleByCompanyIdAndName(
                $companyId,
                InspectionRepresentativeRole::ROLE_SK_CONTRACTOR,
            );
        /** @var InspectionRepresentative $inspectionRepresentative */
        $statusWasChanged = [];
        DB::beginTransaction();
        foreach ($inspectionRepresentatives as $inspectionRepresentative) {
            $statusWasChanged[] = $this
                ->calculateInspectionRepresentativeStatus($inspectionRepresentative, $acceptStatus, $roleSkContractor);
        }

        if (in_array(true, $statusWasChanged)) {
            $this->getCalculateInspectionStatusService()->updateInspectionStatus($inspection);
        }
        DB::commit();
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     * @param InspectionRepresentativeDTO $inspectionRepresentativeDTO
     *
     * @return void
     *
     * @throws Exception
     */
    private function updateStatusAfterElimination(
        InspectionRepresentative $inspectionRepresentative,
        InspectionRepresentativeDTO $inspectionRepresentativeDTO,
    ): void {
        if ($inspectionRepresentativeDTO->hasProperty('status_after_elimination_id')) {
            $this->checkInspectionCancellation($inspectionRepresentative, $inspectionRepresentativeDTO->status_id);
            if ($inspectionRepresentativeDTO->status_after_elimination_id) {
                $inspectionStatus = InspectionStatus::findOrFail(
                    $inspectionRepresentativeDTO->status_after_elimination_id,
                );
                $inspectionRepresentative->statusAfterElimination()->associate($inspectionStatus);
                $inspectionRepresentative->status_after_elimination_updated_at = now();
                if ($inspectionRepresentative->confirmed_date === null) {
                    $this->confirmInspectionRepresentative(
                        $inspectionRepresentative,
                        TaskTypesEnum::INSPECTIONS_CONFIRM_PARTICIPATION->value,
                    );
                }
            }
            if (null === $inspectionRepresentativeDTO->status_after_elimination_id) {
                $inspectionRepresentative->statusAfterElimination()->dissociate();
            }
        }
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     * @param InspectionRepresentativeDTO $inspectionRepresentativeDTO
     *
     * @return void
     *
     * @throws Exception
     * @throws GeneralSettingException
     * @throws InspectionCancellationException
     */
    private function updateStatus(
        InspectionRepresentative $inspectionRepresentative,
        InspectionRepresentativeDTO $inspectionRepresentativeDTO,
    ): void {
        $inspection = $inspectionRepresentative->inspection;

        if ($inspectionRepresentativeDTO->hasProperty('status_id')) {
            if (
                ! $inspectionRepresentative->checkExistingLazyBlockStatus()
                && $inspectionRepresentative->checkAlwaysBlockedStatus()
            ) {
                throw new Exception(self::ALWAYS_BLOCKED_MESSAGE);
            }

            $this->checkInspectionCancellation($inspectionRepresentative, $inspectionRepresentativeDTO->status_id);

            if ($inspectionRepresentativeDTO->status_id) {
                $inspectionStatus = InspectionStatus::findOrFail($inspectionRepresentativeDTO->status_id);

                if (
                    $inspectionStatus->name === InspectionStatus::STATUS_HAS_DEVIATIONS
                    && $inspectionRepresentative->deviationDocs->isEmpty()
                ) {
                    throw new Exception('Добавьте документы с отклонениями');
                }

                $inspectionRepresentative
                    ->status()
                    ->associate($inspectionStatus);

                $this->setLazyBlockCache($inspectionRepresentative, $inspection);

                $inspectionRepresentative->status_updated_at = now();

                $inspectionSituation = InspectionSituation::where(
                    'type_id',
                    InspectionSituation::TYPE_FINISHED_POSITIVELY,
                )
                    ->where(
                        'company_id',
                        $inspection
                            ->object
                            ->getCompanyId(),
                    )
                    ->firstOrFail();

                $statusesIds = $inspectionSituation
                    ->statuses()
                    ->pluck('inspections_situations_link.status_id')
                    ->toArray();

                $this->checkExpiredTime($inspection, $inspectionRepresentative);

                if (in_array($inspectionStatus->getKey(), $statusesIds)) {
                    $this->blockedInspection($inspection, true, $inspectionRepresentative->getkey());

                    $inspectionRepresentative->setStatusApprovalDate(now());
                } else {
                    $this->blockedInspection($inspection, false, $inspectionRepresentative->getkey());

                    $inspectionRepresentative->setStatusApprovalDate(null);
                }
                if ($inspectionRepresentative->confirmed_date === null) {
                    $this->confirmInspectionRepresentative(
                        $inspectionRepresentative,
                        TaskTypesEnum::INSPECTIONS_CONFIRM_PARTICIPATION->value,
                    );
                }
            }

            if (null === $inspectionRepresentativeDTO->status_id) {
                $this->checkExpiredTime($inspection, $inspectionRepresentative);

                $inspectionRepresentative
                    ->status()
                    ->dissociate();

                $this->blockedInspection($inspection, false, $inspectionRepresentative->getkey());

                $inspectionRepresentative->setStatusApprovalDate(null);
            }
        }
    }

    /**
     * @param Inspection $inspection
     * @param string $action
     * @param null|string $oldStatus
     * @param null|string $newStatus
     *
     * @return void
     *
     * @throws Exception
     */
    private function prepareAndSendDataEventLogInspection(
        Inspection $inspection,
        string $action,
        ?string $oldStatus = null,
        ?string $newStatus = null,
    ): void {
        $this->eventLogService->prepareDataAndSendAction(
            Auth::user()->getKey(),
            $action,
            $this->userService,
            $this->eventLogService->prepareEventLogDescriptionForInspectionSmr(
                $inspection,
                $action,
                $oldStatus,
                $newStatus,
            ),
        );
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     *
     * @return string
     */
    private function prepareCurrentInspectionStatus(InspectionRepresentative $inspectionRepresentative): string
    {
        $newStatus = $inspectionRepresentative
            ->status
            ?->name ?? EventLogService::EMPTY_INSPECTION_STATUS;
        if ($inspectionRepresentative->description !== null) {
            $newStatus .= '/' . PHP_EOL . $inspectionRepresentative->description;
        }

        return $newStatus;
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     *
     * @return string
     */
    private function preparePreviousInspectionStatus(InspectionRepresentative $inspectionRepresentative): string
    {
        $oldStatus = $inspectionRepresentative
            ->status
            ?->name ?? EventLogService::EMPTY_INSPECTION_STATUS;

        if ($inspectionRepresentative->description !== null) {
            $oldStatus .= '/' . PHP_EOL . $inspectionRepresentative->description;
        }

        return $oldStatus;
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     *
     * @return InspectionRepresentative
     */
    private function checkInspectionRepresentative(
        InspectionRepresentative $inspectionRepresentative,
    ): InspectionRepresentative {
        if (
            $inspectionRepresentative->status_id === null
            && $inspectionRepresentative->status_after_elimination_id === null
        ) {
            $inspectionRepresentative->representative_info = null;
        }

        return $inspectionRepresentative;
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     *
     * @return bool
     */
    private function isInspectionRepresentativeAbleToCancelParticipation(
        InspectionRepresentative $inspectionRepresentative,
    ): bool {
        return ! (isset($inspectionRepresentative->cancel_date));
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     * @param string $comment
     *
     * @return void
     */
    private function cancelInspectionRepresentativeParticipation(
        InspectionRepresentative $inspectionRepresentative,
        string $comment,
    ): void {
        $inspectionRepresentative->cancel_date = now();
        $inspectionRepresentative->cancel_comment = $comment;
        $inspectionRepresentative->save();
    }

    /**
     * @param Inspection $inspection
     * @param Collection $documents
     *
     * @return void
     *
     * @throws RepresentativeDeviationDocsAttachingExpection
     */
    private function checkDeviationDocsAbleToAttach(Inspection $inspection, Collection $documents): void
    {
        $inspectionAttachmentsIds = $inspection->attachments()->pluck('documents_files.id')->toArray();
        $attachingDocumentIds = $documents->pluck('id')->toArray();
        $notAbleToAttach = count(array_diff($attachingDocumentIds, $inspectionAttachmentsIds)) > 0;

        if ($notAbleToAttach) {
            throw new RepresentativeDeviationDocsAttachingExpection();
        }
    }

    /**
     * @throws InspectionCancellationException
     */
    private function checkInspectionCancellation(
        InspectionRepresentative $inspectionRepresentative,
        ?int $statusId,
    ): void {
        if ($statusId === null) {
            return;
        }

        $cancelStatus = $this
            ->inspectionStatusRepository
            ->getCancelStatus($inspectionRepresentative->inspection->object->company_id);
        if ($cancelStatus === null) {
            return;
        }

        if ($statusId === $cancelStatus->id && $inspectionRepresentative->description === null) {
            throw new InspectionCancellationException();
        }
    }

    /**
     * @param InspectionRepresentative $representative
     * @param Inspection $inspection
     *
     * @return void
     */
    private function setLazyBlockCache(InspectionRepresentative $representative, Inspection $inspection): void
    {
        $setting = $this->getSettingStatusLazyBlock($inspection);
        if ($setting !== null && $setting->is_enabled === true) {
            $statuses = $setting->layersForInspectionStatus->pluck('inspectionStatus');
            if ($setting->is_enabled === true) {
                if ($statuses->contains('name', $representative->status->name)) {
                    if (! $representative->is_always_blocked) {
                        $representative->is_always_blocked = true;
                        Redis::setEx(
                            "inspections:lazy-block:{$inspection->getKey()}:{$representative->getKey()}:in-time",
                            60 * $setting->time,
                            'status lazy blocked',
                        );
                    }
                }
            }
            if (
                $representative->is_always_blocked === true
                && $representative->checkExistingLazyBlockStatus()
                && ! $statuses->contains('name', $representative->status->name)
            ) {
                $representative->is_always_blocked = false;
                Redis::del(
                    "inspections:lazy-block:{$inspection->getKey()}:{$representative->getKey()}:in-time",
                );
            }
        }
    }

    /**
     * @param Inspection $inspection
     *
     * @return null|SettingInspectionChangeStatusLazyBlock
     */
    private function getSettingStatusLazyBlock(Inspection $inspection): ?SettingInspectionChangeStatusLazyBlock
    {
        $setting = SettingInspectionChangeStatusLazyBlock::where('company_id', $inspection->object->company_id)->first();

        return $setting?->load('layersForInspectionStatus');
    }

    /**
     * @param InspectionRepresentative $inspectionRepresentative
     * @param InspectionRepresentativeDTO $dto
     *
     * @return void
     */
    private function updateCancelReason(
        InspectionRepresentative $inspectionRepresentative,
        InspectionRepresentativeDTO $dto,
    ): void {
        if (! $dto->hasProperty('cancel_reason_id')) {
            return;
        }

        $inspectionRepresentative->cancelReason()->associate($dto->cancel_reason_id);
    }
}
